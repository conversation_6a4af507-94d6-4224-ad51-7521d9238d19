const {
  ValidationError,
  DatabaseError,
  ConnectionError,
} = require("sequelize");
const httpStatus = require("http-status");
const config = require("../config/appConfig");
const logger = require("../config/logger");
const { sendError, ApiError } = require("../helpers/api.helper");

/**
 * Error converter middleware
 * Converts different types of errors to ApiError instances
 */
const errorConverter = (err, req, res, next) => {
  let error = err;
  
  if (!(error instanceof ApiError)) {
    let statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    let message = httpStatus[statusCode];

    // Handle Sequelize validation errors
    if (error instanceof ValidationError) {
      statusCode = httpStatus.BAD_REQUEST;
      message = error.errors ? error.errors.map(e => e.message).join(', ') : error.message;
    } 
    // Handle Sequelize database errors
    else if (error instanceof DatabaseError) {
      statusCode = httpStatus.INTERNAL_SERVER_ERROR;
      message = config.env === 'production' ? 'Database error occurred' : error.message;
    } 
    // Handle Sequelize connection errors
    else if (error instanceof ConnectionError) {
      statusCode = httpStatus.INTERNAL_SERVER_ERROR;
      message = "Database connection error";
    } 
    // Handle JWT errors
    else if (error.name === 'JsonWebTokenError') {
      statusCode = httpStatus.UNAUTHORIZED;
      message = 'Invalid token';
    }
    else if (error.name === 'TokenExpiredError') {
      statusCode = httpStatus.UNAUTHORIZED;
      message = 'Token expired';
    }
    // Handle validation errors from Joi
    else if (error.name === 'ValidationError' && error.details) {
      statusCode = httpStatus.BAD_REQUEST;
      message = error.details.map(detail => detail.message).join(', ');
    }
    // Handle other errors with statusCode
    else if (error.statusCode) {
      statusCode = error.statusCode;
      message = error.message || httpStatus[statusCode];
    }

    error = new ApiError(statusCode, message, false, err.stack);
  }
  
  next(error);
};

/**
 * Error handler middleware
 * Sends error response to client
 */
const errorHandler = (err, req, res, next) => {
  let { statusCode, message } = err;
  
  // In production, don't expose internal errors
  if (config.env === "production" && !err.isOperational) {
    statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    message = httpStatus[httpStatus.INTERNAL_SERVER_ERROR];
  }

  res.locals.errorMessage = err.message;

  let data = [];
  // Include stack trace in development/local environment
  if (config.env === "development" || config.env === "local") {
    logger.error(err);
    data = err.stack;
  }

  return sendError(res, message, statusCode, data);
};

module.exports = {
  errorConverter,
  errorHandler,
};
