const morgan = require('morgan');
const config = require('./appConfig');
const logger = require('./logger');

morgan.token('message', (req, res) => res.locals.errorMessage || '');

const successHandler = morgan('combined', {
  skip: (req, res) => res.statusCode >= 400,
  stream: { write: (message) => logger.info(message.trim()) },
});

const errorHandler = morgan('combined', {
  skip: (req, res) => res.statusCode < 400,
  stream: { write: (message) => logger.error(message.trim()) },
});

module.exports = {
  successHandler,
  errorHandler,
};
