const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const httpStatus = require('http-status');
const db = require('./models');
const config = require('./config/appConfig');
const morgan = require('./config/morgan');
// const { apiLimiter, authLimiter, passwordResetLimiter } = require('./middleware/rateLimiter');
const { errorConverter, errorHandler } = require('./middleware/error');
const { ApiError } = require('./helpers/api.helper');

// Routes
const authRoutes = require('./routes/auth.route');
const userRoutes = require('./routes/user.routes');
const adminRoutes = require('./routes/admin.routes');
const planRoutes = require('./routes/plan.routes');
const investmentRoutes = require('./routes/investment.routes');
const bonusRoutes = require('./routes/bonus.routes');
const passwordResetRoutes = require('./routes/passwordReset.routes');

// 📦 Load environment variables from .env
dotenv.config();

// 🚀 Initialize app
const app = express();

// 🛡️ Security and Logging Middleware
// Request logging
app.use(morgan.successHandler);
app.use(morgan.errorHandler);

// Set security HTTP headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      connectSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https:", "data:"]
    }
  }
}));

// Parse JSON request body
app.use(express.json({ limit: '10mb' }));

// Parse urlencoded request body
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Enable CORS
app.use(cors());
app.options("*", cors());

// General rate limiting
// app.use("/", apiLimiter);

// 🔗 Routes

// API Routes
app.use('/api', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/plans', planRoutes);
app.use('/api/investments', investmentRoutes);
app.use('/api/bonuses', bonusRoutes);
app.use('/', passwordResetRoutes);

// 🧠 Default route
app.get('/', (req, res) => {
    res.json({
        status: true,
        message: 'Welcome to Gold Trading Platform API!',
        data: {
            version: '1.0.0',
            environment: config.env,
            timestamp: new Date().toISOString()
        }
    });
});

// Send back a 404 error for any unknown API request
app.use((req, res, next) => {
    next(new ApiError(httpStatus.NOT_FOUND, "API endpoint not found"));
});

// Convert error to ApiError, if needed
app.use(errorConverter);

// Handle error
app.use(errorHandler);

// ⚙️ Connect to DB and start server
const PORT = config.port;

// Test database connection first
db.sequelize.sync()
    .then(() => {
        console.log('✅ Database connection established successfully');

        // Use force: false and alter: false to avoid index conflicts
        // For production, use migrations instead of sync
        return db.sequelize.sync({ force: false, alter: false });
    })
    .then(() => {
        // console.log('✅ Database synchronized');

        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
        });
    })
    .catch(err => {
        console.error('❌ Failed to connect to DB:', err.message);
        console.error('Full error:', err);
        process.exit(1);
    });
