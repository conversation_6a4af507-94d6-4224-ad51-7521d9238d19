const logger = require('../config/logger');
const { sequelize } = require('../models');

/**
 * A helper to catch asynchronous errors and optionally manage a transaction.
 *
 * @param {Function} fn - The async route handler (expects req, res, next).
 * @param {Object} [options] - Optional settings.
 * @param {boolean} [options.useTransaction=false] - If true, the handler is run in a new transaction.
 * @returns {Function} A function wrapping the original handler.
 */
const catchAsync = (fn, options = {}) => {
  const { useTransaction = false } = options;
  return async (req, res, next) => {
    if (!useTransaction) {
      // If transaction management is not desired, simply run the function.
      Promise.resolve(fn(req, res, next)).catch(next);
      return;
    }

    // Otherwise, create a new transaction.
    const transaction = await sequelize.transaction();
    req.transaction = transaction;

    try {
      // Add updated_by field if user is authenticated and it's a modifying request
      if (req.user && req.body && ["POST", "PUT", "PATCH", "DELETE"].includes(req.method.toUpperCase())) {
        req.body.updated_by = req.user.id;
      }
      
      await fn(req, res, next);
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      next(error);
    }
  };
};

/**
 * Custom API Error class
 */
class ApiError extends Error {
  constructor(statusCode, message, isOperational = true, stack = '') {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Success Response Function
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {number} code - HTTP status code (default: 200)
 * @param {*} data - Response data (default: [])
 */
const sendSuccess = (res, message, code = 200, data = []) => {
  res.status(code).json({
    status: true,
    data,
    message,
  });
};

/**
 * Error Response Function
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} code - HTTP status code (default: 400)
 * @param {*} data - Error data (default: [])
 */
const sendError = (res, message, code = 400, data = []) => {
  res.status(code).json({
    status: false,
    message,
    data,
  });
};

module.exports = { ApiError, catchAsync, sendSuccess, sendError };
