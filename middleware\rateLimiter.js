const rateLimit = require('express-rate-limit');
const httpStatus = require('http-status');

/**
 * General API rate limiter
 * Limits requests per IP address
 */
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    status: false,
    message: 'Too many requests from this IP, please try again later.',
    data: []
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  statusCode: httpStatus.TOO_MANY_REQUESTS,
});

/**
 * Strict rate limiter for authentication endpoints
 * More restrictive for login/register attempts
 */
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
  message: {
    status: false,
    message: 'Too many authentication attempts, please try again later.',
    data: []
  },
  standardHeaders: true,
  legacyHeaders: false,
  statusCode: httpStatus.TOO_MANY_REQUESTS,
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * Password reset rate limiter
 * Very restrictive for password reset attempts
 */
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    status: false,
    message: 'Too many password reset attempts, please try again later.',
    data: []
  },
  standardHeaders: true,
  legacyHeaders: false,
  statusCode: httpStatus.TOO_MANY_REQUESTS,
});

module.exports = {
  apiLimiter,
  authLimiter,
  passwordResetLimiter,
};
